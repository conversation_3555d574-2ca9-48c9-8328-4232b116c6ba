import clsx from 'clsx';
import {
  Suspense,
  useCallback,
  useEffect,
  useState,
} from 'react';
import {Switch} from 'react-aria-components';
import {
  useLocation,
  useNavigate,
  useParams,
  useSearchParams,
} from 'react-router';

import {useQuery} from '@apollo/client';
import {Redo2, Undo2} from 'lucide-react';
import {observer} from 'mobx-react-lite';

import PaperIcon from '@/assets/iconly/Paper.svg?react';
import SearchChatIcon from '@/assets/iconly/SearchChat.svg?react';
import TrendDownGraphIcon from '@/assets/iconly/TrendDownGraph.svg?react';
import {Button} from '@/components/ui/button.tsx';
import {useSidebar} from '@/components/ui/sidebar.tsx';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs.tsx';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip.tsx';
import {useRftReport} from '@/features/studies-rft/report';
import {ReportSidebar} from '@/features/studies-rft/sections/report-sidebar.tsx';
import {RftStore} from '@/features/studies-rft/store/rtf.store.ts';
import {ReportPatientInfo} from '@/features/studies/ReportPatientInfo.tsx';
import {undoStack} from '@/features/undo-stack';
import {getPatientsDetailData} from '@/graphql/patients.ts';
import {useGlobalStoreLoader} from '@/store/global.store.ts';
import PatientTrendTable from '@/views/patients/patient-detail/patient-trend-view/components/patient-trend-table.tsx';

import {BloodGasesSection} from './sections/blood-gases.tsx';
import {COTransferSection} from './sections/co-transfer.tsx';
import {ExhaledNitricOxideSection} from './sections/exhaled-nitric-oxide.tsx';
import {FlowVolume} from './sections/flow-volume.tsx';
import {LungVolumesSection} from './sections/lung-volumes.tsx';
import {MRPsSection} from './sections/mrps.tsx';
import {SpirometryFormSection} from './sections/spirometry.tsx';
import './styles.css';
import {Image} from "@react-pdf/renderer";
import {toPng} from "html-to-image";
import {tw} from "@/lib/react-pdf-tailwind.ts";

function TestSections({rftStore}: {rftStore: RftStore}) {
  useGlobalStoreLoader();
  return (
    <div className="relative space-y-3 rounded border border-neutral-200 bg-white p-4">
      <SpirometryFormSection rftStore={rftStore} />
      <COTransferSection rftStore={rftStore} />
      <LungVolumesSection rftStore={rftStore} />
      <ExhaledNitricOxideSection rftStore={rftStore} />
      <MRPsSection rftStore={rftStore} />
      <BloodGasesSection rftStore={rftStore} />
      <FlowVolume rftStore={rftStore} />
    </div>
  );
}

const RFTStudyDetail = observer(() => {
  type UploadResponse = {
    success?: boolean;
    error?: string;
    message?: string;
    original?: any;
  };
  const {id, patientId} = useParams<{id: string; patientId: string}>();
  const {toggleSidebar, state} = useSidebar();
  const {createReport, isLoading} = useRftReport();
  const [searchParams] = useSearchParams();
  const editParam = searchParams.get('edit');
  const [isTrendViewActive, setIsTrendViewActive] = useState(false);
  const isEditing = editParam === 'true';
  const [confirmMessage, setConfirmMessage] = useState('');
  useQuery(getPatientsDetailData, {variables: {patientId: +patientId!}});

  const [rftStore, setRftStore] = useState(() => {
    return new RftStore({rftid: +id!, isEditing});
  });

  const [dragging, setDragging] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [pendingFile, setPendingFile] = useState<File | null>(null);
  const [pdfUploaded, setPdfUploaded] = useState(false);
  const [undoingUpload, setUndoingUpload] = useState(false);
  const [originalData, setOriginalData] = useState(null);
  useEffect(() => {
    undoStack.reset();
    setRftStore(new RftStore({rftid: +id!, isEditing}));
    return () => {
      rftStore.dispose();
      undoStack.reset();
    };
  }, []);

  useEffect(() => {
    if (state === 'expanded') {
      toggleSidebar();
    }
  }, []);


  const handleUndoUpload = async () => {
    if (!originalData) {
      setSuccessMessage('❌ No original data to revert to');
    }

    setUndoingUpload(true);
    try {
      const response = await fetch('/api/pdf_import/undo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rft_id: id,
          original: originalData,
        }),
      });

      if (!response.ok) throw new Error('Undo failed');
      const result = await response.json() as UploadResponse;
      if (result.success) {
        setSuccessMessage('✅ PDF import undone');
        setPdfUploaded(false);
      } else {
        setSuccessMessage('❌ Failed to undo import');
      }
    } catch (err) {
      console.error(err);
      setSuccessMessage('❌ Error occurred during undo');
    } finally {
      setUndoingUpload(false);
      setTimeout(() => setSuccessMessage(''), 5000);
    }
  };
  const handleSubmit = useCallback(
    async (file: File, isOverwrite = false) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('id', id!);
      formData.append('patientId', patientId!);
      formData.append('is_overwrite', String(isOverwrite));
      try {
        const response = await fetch('/api/pdf_import/patient', {
          method: 'POST',
          body: formData,
        });

        const result = await response.json() as UploadResponse;

        if (response.status === 200 && result.error === 'Patient mismatch') {
          setPendingFile(file);
          setConfirmMessage(result.message || 'Patient data does not match. Do you want to overwrite the existing patient data?');
          setConfirmOpen(true);
          return;
        }

        if (!response.ok) throw new Error(result.error || 'Upload failed');
        if (result.success) {
          setOriginalData(result.original);  // Store original RFT data
          setSuccessMessage('✅ Patient data extracted');
          setPdfUploaded(true);
        }
        setSuccessMessage('✅ Patient data extracted');
        setPdfUploaded(true);
      } catch (err) {
        console.error(err);
        setSuccessMessage('❌ Failed to extract patient data');
      } finally {
        setTimeout(() => setSuccessMessage(''), 5000);
      }
    },
    [id, patientId]
  );

  const onDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragging(false);
      const file = e.dataTransfer.files[0];
      if (file && file.type === 'application/pdf') {
        handleSubmit(file);
      } else {
        setSuccessMessage('❌ Only PDF files are accepted');
        setTimeout(() => setSuccessMessage(''), 5000);
      }
    },
    [handleSubmit]
  );

  const onDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!dragging) setDragging(true);
  };

  const onDragLeave = () => {
    setDragging(false);
  };

  return (
    <div
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
      className={clsx('relative', dragging && 'ring-2 ring-blue-500 ring-offset-2')}
    >
      {dragging && (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm border-4 border-dashed border-blue-400 text-blue-600 text-xl font-semibold pointer-events-none">
          Drop PDF file to upload
        </div>
      )}

      {successMessage && (
        <div className="absolute top-4 right-4 z-50 rounded bg-white px-4 py-2 shadow-lg border">
          {successMessage}
        </div>
      )}

      {confirmOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-lg">
            <h2 className="text-lg font-semibold text-neutral-900 mb-3">Confirm Overwrite</h2>
            <p className="text-neutral-700 mb-5">{confirmMessage}</p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setConfirmOpen(false);
                  setPendingFile(null);
                  setSuccessMessage('❌ Upload cancelled due to mismatch');
                }}
                className="px-4 py-2 text-sm rounded border border-neutral-300 hover:bg-neutral-100"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setConfirmOpen(false);
                  if (pendingFile) handleSubmit(pendingFile, true);
                }}
                className="px-4 py-2 text-sm text-white bg-blue-600 hover:bg-blue-700 rounded"
              >
                Overwrite
              </button>
            </div>
          </div>
        </div>
      )}

      <ReportPatientInfo testSession={rftStore.testSession} />
      <Tabs
        defaultValue="results"
        onValueChange={(value) => setIsTrendViewActive(value === 'trend-view')}
      >
        <div className="mb-4 flex items-center gap-x-5">
          <RftStudyToolbar
            rftStore={rftStore}
            onUndoUpload={handleUndoUpload}
            pdfUploaded={pdfUploaded}
            originalData={originalData}
            undoingUpload={undoingUpload}
          />


          <div className="flex-1" />
          <Button
            variant="outlined"
            size="small"
            isDisabled={isLoading}
            isPending={isLoading}
            onPress={async () => {

              const spZscoreEl = document.getElementById('sp-zscore-plot');
              const coZscoreEl = document.getElementById('co-zscore-plot');
              const lvZscoreEl = document.getElementById('lv-zscore-plot');

              const spZscoreImage = spZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(spZscoreEl)} /> : null;
              const coZscoreImage = coZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(coZscoreEl)} /> : null;
              const lvZscoreImage = lvZscoreEl ? <Image style={tw('w-[140px]')} src={await toPng(lvZscoreEl)} /> : null;

              createReport({rftStore, spZscoreImage, coZscoreImage, lvZscoreImage}).then((url) => {
                window.open(url);
              });
            }}
          >
            <PaperIcon />
            {isLoading ? 'Compiling Report...' : 'View Report'}
          </Button>
        </div>

        <div className="flex items-start gap-x-3">
          <div
            className={clsx(
              'study-results w-278 shrink-0',
              isTrendViewActive ? 'sticky top-13.5 isolate z-10' : ''
            )}
          >
            <Suspense fallback={<div>Loading...</div>}>
              <TabsContent className="mt-0" value="results">
                <TestSections rftStore={rftStore} />
              </TabsContent>
              <TabsContent className="mt-0" value="trend-view">
                <PatientTrendTable />
              </TabsContent>
            </Suspense>
          </div>
          <ReportSidebar rftStore={rftStore} />
        </div>
      </Tabs>
    </div>
  );
});

const RftStudyToolbar = observer(({
  rftStore,
  onUndoUpload,
  pdfUploaded,
  originalData,
  undoingUpload
}: {
  rftStore: RftStore;
  onUndoUpload: () => void;
  pdfUploaded: boolean;
  originalData: any;
  undoingUpload: boolean;
}) => {

  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    if (rftStore.isEditing) {
      searchParams.set('edit', 'true');
    } else {
      searchParams.delete('edit');
    }
    navigate(
      {
        pathname: location.pathname,
        search: searchParams.toString(),
      },
      {replace: true}
    );
  }, [rftStore.isEditing]);

  return (
    <>
      <TabsList className="flex w-fit justify-start">
        <TabsTrigger
          className="flex cursor-pointer gap-x-2 font-normal text-neutral-700 data-[state=active]:font-medium data-[state=active]:text-neutral-800"
          value="results"
        >
          <SearchChatIcon className="size-4.5" />
          Results
        </TabsTrigger>
        <TabsTrigger
          className="flex cursor-pointer gap-x-2 font-normal text-neutral-700 data-[state=active]:font-medium data-[state=active]:text-neutral-800"
          value="trend-view"
        >
          <TrendDownGraphIcon className="size-4.5" />
          Trend View
        </TabsTrigger>
      </TabsList>

      <Tooltip>
        <TooltipTrigger asChild>
          <div>
            <Switch
              isSelected={rftStore.isEditing}
              onChange={(isSelected) =>
                rftStore.setProperty('isEditing', isSelected)
              }
              isDisabled={!rftStore.isReportEditable}
              isReadOnly={!rftStore.isReportEditable}
            >
              <div>Edit Mode</div>
              <div className="flex h-7.5 items-center gap-x-2 rounded border border-neutral-300 px-2">
                <div className="w-5 text-center text-[10px] text-neutral-600">
                  <div className="block [[data-selected]_&]:hidden">OFF</div>
                  <div className="hidden [[data-selected]_&]:block">ON</div>
                </div>
                <div className="indicator" />
              </div>
            </Switch>
          </div>
        </TooltipTrigger>
        {!rftStore.isReportEditable && (
          <TooltipContent>
            You cannot edit a completed report. Try amending instead.
          </TooltipContent>
        )}
      </Tooltip>

      {rftStore.isEditing && (
        <div className="flex items-center gap-x-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canUndo}
                onClick={() => undoStack.undo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canUndo && 'cursor-default'
                )}
              >
                <Undo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Undo</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!undoStack.canRedo}
                onClick={() => undoStack.redo()}
                className={clsx(
                  'text-brand-600 cursor-pointer disabled:text-neutral-500',
                  !undoStack.canRedo && 'cursor-default'
                )}
              >
                <Redo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Redo</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                disabled={!pdfUploaded || !originalData || undoingUpload}
                onClick={onUndoUpload}
                className={clsx(
                'cursor-pointer text-brand-600 disabled:text-neutral-500',
                (!pdfUploaded || !originalData || undoingUpload) && 'cursor-default' && 'hidden'
                )}
              >
              <Undo2 className="size-4.5" />
              </button>
            </TooltipTrigger>
            <TooltipContent>Undo PDF Import</TooltipContent>
          </Tooltip>
        </div>
      )}
    </>
  );
});

export default RFTStudyDetail;